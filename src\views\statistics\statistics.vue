<template>
    <div class="app-container statistics-container">
        <!-- 外部联系人数量卡片 -->
        <div class="statistics-card compact">
            <div class="card-header compact">
                <el-icon class="card-icon">
                    <User />
                </el-icon>
                <h3 class="card-title">外部联系人数量</h3>
            </div>

            <!-- 统计数据和图表同排显示 -->
            <div class="contact-stats-row">
                <!-- 统计数据 -->
                <div class="stats-section">
                    <div class="stat-item compact">
                        <div class="stat-label">当前数量</div>
                        <div class="stat-value current-count">{{ contactStats.currentCount }}</div>
                    </div>
                    <div class="stat-item compact">
                        <div class="stat-label">跟昨日相比</div>
                        <div class="stat-value" :class="contactStats.compareYesterday >= 0 ? 'positive' : 'negative'">
                            {{ contactStats.compareYesterday >= 0 ? '+' : '' }}{{ contactStats.compareYesterday }}
                        </div>
                    </div>
                </div>

                <!-- 柱状图 -->
                <div class="chart-section">
                    <div class="chart-title-small">企业人数</div>
                    <div ref="companyCountChart" class="chart compact"></div>
                </div>
            </div>
        </div>

        <!-- 发送信息数量和接收人发送次数 -->
        <div class="cards-row compact">
            <div class="statistics-card compact half-width">
                <div class="card-header compact">
                    <el-icon class="card-icon">
                        <Message />
                    </el-icon>
                    <h3 class="card-title">发送信息数量</h3>
                </div>
                <div class="single-stat compact">
                    <div class="stat-value">{{ messageStats.totalSent }}</div>
                    <div class="stat-label">条消息</div>
                </div>
            </div>

            <div class="statistics-card compact half-width">
                <div class="card-header compact">
                    <el-icon class="card-icon">
                        <ChatDotRound />
                    </el-icon>
                    <h3 class="card-title">接收人发送次数</h3>
                </div>
                <div class="single-stat compact">
                    <div class="stat-value">{{ messageStats.receiverSendCount }}</div>
                    <div class="stat-label">次发送</div>
                </div>
            </div>
        </div>

        <!-- 不同属性推送次数和移除通讯录 -->
        <div class="cards-row compact">
            <div class="statistics-card compact half-width">
                <div class="card-header compact">
                    <el-icon class="card-icon">
                        <PieChart />
                    </el-icon>
                    <h3 class="card-title">不同属性推送次数</h3>
                </div>
                <div ref="attributePushChart" class="chart compact pie-chart"></div>
            </div>

            <div class="statistics-card compact half-width">
                <div class="card-header compact">
                    <el-icon class="card-icon">
                        <Delete />
                    </el-icon>
                    <h3 class="card-title">移除通讯录</h3>
                </div>
                <div ref="removeContactChart" class="chart compact pie-chart"></div>
            </div>
        </div>
    </div>
</template>

<script setup name="Statistics">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { User, Message, ChatDotRound, PieChart, Delete } from '@element-plus/icons-vue'

// 图表实例
const companyCountChart = ref(null)
const attributePushChart = ref(null)
const removeContactChart = ref(null)

let companyChart = null
let attributeChart = null
let removeChart = null

// 模拟数据
const contactStats = ref({
    currentCount: 1248,
    compareYesterday: 32
})

// 企业人数数据（按人数从高到低排序）
const companyData = ref([
    { name: '华为技术有限公司', count: 156 },
    { name: '腾讯科技有限公司', count: 142 },
    { name: '阿里巴巴集团', count: 128 },
    { name: '百度在线网络技术', count: 115 },
    { name: '字节跳动科技', count: 98 },
    { name: '美团点评', count: 87 },
    { name: '京东集团', count: 76 },
    { name: '滴滴出行科技', count: 65 },
    { name: '华为技术有限公司', count: 156 },
    { name: '腾讯科技有限公司', count: 142 },
    { name: '阿里巴巴集团', count: 128 },
    { name: '百度在线网络技术', count: 115 },
    { name: '字节跳动科技', count: 98 },
    { name: '美团点评', count: 87 },
    { name: '京东集团', count: 76 },
    { name: '滴滴出行科技', count: 65 },
    { name: '华为技术有限公司', count: 156 },
    { name: '腾讯科技有限公司', count: 142 },
    { name: '阿里巴巴集团', count: 128 },
    { name: '百度在线网络技术', count: 115 },
    { name: '字节跳动科技', count: 98 },
    { name: '美团点评', count: 87 },
    { name: '京东集团', count: 76 },
    { name: '滴滴出行科技', count: 65 }
])


const messageStats = ref({
    totalSent: 5678,
    receiverSendCount: 892
})
// 不同属性推送次数数据
const attributePushData = ref([
    { name: '行业', value: 1234 },
    { name: '主管部门', value: 856 },
    { name: '属地', value: 642 }
])

// 移除通讯录数据
const removeContactData = ref([
    { name: '企业删除', value: 45 },
    { name: '发改委删除', value: 23 }
])

// 初始化企业人数柱状图
const initCompanyChart = () => {
    if (!companyCountChart.value) return

    companyChart = echarts.init(companyCountChart.value)

    // 定义颜色数组：红、黄、蓝、绿轮询
    const colors = ['#F56C6C', '#E6A23C', '#409EFF', '#67C23A']

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '2%',
            right: '2%',
            bottom: '5%',
            top: '19%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: companyData.value.map(item => item.name),
            axisLabel: {
                rotate: 30,
                fontSize: 9,
                interval: 0
            }
        },
        yAxis: {
            type: 'value',
            name: '人数',
            nameTextStyle: {
                fontSize: 10
            },
            axisLabel: {
                fontSize: 9
            }
        },
        series: [{
            name: '人数',
            type: 'bar',
            data: companyData.value.map((item, index) => ({
                value: item.count,
                itemStyle: {
                    color: colors[index % colors.length]
                }
            })),
            barWidth: '20%', // 缩小柱体宽度
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    }

    companyChart.setOption(option)
}

// 初始化不同属性推送次数饼图
const initAttributeChart = () => {
    if (!attributePushChart.value) return

    attributeChart = echarts.init(attributePushChart.value)

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            top: '5%',
            left: 'center',
            itemGap: 20,
            textStyle: {
                fontSize: 11
            }
        },
        series: [{
            name: '推送次数',
            type: 'pie',
            radius: ['30%', '65%'], // 内外半径，创建环形图
            center: ['50%', '60%'],
            data: attributePushData.value,
            itemStyle: {
                borderRadius: 8, // 圆角效果
                borderColor: '#fff',
                borderWidth: 3, // 间隙效果
                color: function (params) {
                    const colors = ['#409EFF', '#67C23A', '#E6A23C']
                    return colors[params.dataIndex]
                }
            },
            emphasis: {
                itemStyle: {
                    borderRadius: 10 // 悬停时更大的圆角
                }
            },
            label: {
                fontSize: 10
            }
        }]
    }

    attributeChart.setOption(option)
}

// 初始化移除通讯录饼图
const initRemoveChart = () => {
    if (!removeContactChart.value) return

    removeChart = echarts.init(removeContactChart.value)

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            top: '5%',
            left: 'center',
            itemGap: 20,
            textStyle: {
                fontSize: 11
            }
        },
        series: [{
            name: '移除数量',
            type: 'pie',
            radius: ['30%', '65%'], // 内外半径，创建环形图
            center: ['50%', '60%'],
            data: removeContactData.value,
            itemStyle: {
                borderRadius: 8, // 圆角效果
                borderColor: '#fff',
                borderWidth: 3, // 间隙效果
                color: function (params) {
                    const colors = ['#F56C6C', '#E6A23C']
                    return colors[params.dataIndex]
                }
            },
            emphasis: {
                itemStyle: {
                    borderRadius: 10 // 悬停时更大的圆角
                }
            },
            label: {
                fontSize: 10
            }
        }]
    }

    removeChart.setOption(option)
}

// 窗口大小改变时重新调整图表
const handleResize = () => {
    companyChart?.resize()
    attributeChart?.resize()
    removeChart?.resize()
}

onMounted(() => {
    nextTick(() => {
        initCompanyChart()
        initAttributeChart()
        initRemoveChart()

        window.addEventListener('resize', handleResize)
    })
})

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    companyChart?.dispose()
    attributeChart?.dispose()
    removeChart?.dispose()
})
</script>

<style scoped lang="scss">
.statistics-container {
    padding: 12px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 84px);
}

.statistics-card {
    background: #ffffff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    &.compact {
        padding: 12px;
        margin-bottom: 8px;
    }

    &.half-width {
        width: calc(50% - 6px);
    }

    &.quarter-width {
        width: calc(25% - 9px);
    }
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &.compact {
        margin-bottom: 8px;
    }

    .card-icon {
        font-size: 16px;
        color: #409EFF;
        margin-right: 8px;
    }

    .card-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }
}

// 外部联系人数量卡片的特殊布局
.contact-stats-row {
    display: flex;
    gap: 16px;
    align-items: stretch;
}

.stats-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px;
}

.chart-section {
    flex: 1;
    min-height: 280px;
    position: relative;
}

.chart-title-small {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 12px;
    font-weight: 600;
    color: #303133;
    z-index: 10;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;

    &.compact {
        padding: 8px;
        height: 50%;
        display: flex;
        flex-wrap: wrap;
        align-content: center;
    }

    .stat-label {
        width: 100%;
        font-size: 16px;
        color: #606266;
        margin-bottom: 4px;
    }

    .stat-value {
        width: 100%;
        font-size: 23px;
        font-weight: bold;
        color: #303133;

        &.current-count {
            color: #409EFF;
        }

        &.positive {
            color: #67C23A;
        }

        &.negative {
            color: #F56C6C;
        }
    }
}

.cards-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    &.compact {
        gap: 8px;
        margin-bottom: 8px;
    }
}

.single-stat {
    text-align: center;
    padding: 16px 12px;

    &.compact {
        padding: 2vh 8px;
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 1vh;
    }

    .stat-label {
        font-size: 12px;
        color: #606266;
    }
}

.chart {
    height: 180px;
    width: 100%;

    &.compact {
        height: 280px;
    }

    &.pie-chart {
        height: 26vh;
    }
}

// 响应式设计

@media (max-width: 1200px) {
    .contact-stats-row {
        flex-direction: column;
        gap: 12px;
    }

    .stats-section {
        flex-direction: row;
        min-width: auto;
    }

    .chart-section {
        min-height: 260px;
    }

    .cards-row {
        flex-direction: column;

        .statistics-card.half-width {
            width: 100%;
        }
    }
}

@media (max-width: 768px) {
    .statistics-container {
        padding: 8px;
    }

    .statistics-card {
        padding: 12px;

        &.compact {
            padding: 10px;
        }
    }

    .card-header {
        .card-title {
            font-size: 13px;
        }

        .card-icon {
            font-size: 14px;
        }
    }

    .stat-value {
        font-size: 18px !important;
    }

    .chart {
        height: 140px;

        &.compact {
            height: 120px;
        }

        &.pie-chart {
            height: 180px;
        }
    }

    .stats-section {
        flex-direction: column;
        gap: 6px;
    }

    .contact-stats-row {
        gap: 8px;
    }
}
</style>