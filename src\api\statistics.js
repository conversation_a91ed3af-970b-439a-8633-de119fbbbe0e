import request from '@/utils/request'

export function msgAnalysis(query) {
    return request({
        url: '/wechat/statApi/msgAnalysis',
        method: 'get',
        params: query
    })
}

export function msgSendAnalysis(query) {
    return request({
        url: '/wechat/statApi/msgSendAnalysis',
        method: 'get',
        params: query
    })
}

export function userAnalysis(query) {
    return request({
        url: '/wechat/statApi/userAnalysis',
        method: 'get',
        params: query
    })
}
